<!DOCTYPE html>
<html lang="en" data-theme="light">
  <head>
    <meta charset="UTF-8" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0, user-scalable=no"
    />
    <title>Smart QR ID Scanner - Professional ID Management System</title>
    <meta
      name="description"
      content="Modern QR code ID scanning and printing system with real-time processing"
    />

    <!-- Favicon -->
    <link
      rel="icon"
      type="image/png"
      href="{{ url_for('static', filename='trademark/blurbgicon.png') }}"
    />
    <link
      rel="apple-touch-icon"
      href="{{ url_for('static', filename='trademark/blurbgicon.png') }}"
    />

    <!-- Preload Critical Resources -->
    <link
      rel="preload"
      href="{{ url_for('static', filename='css/index.css') }}"
      as="style"
    />
    <link
      rel="preload"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css"
      as="style"
    />

    <!-- External Libraries -->
    <script src="https://unpkg.com/html5-qrcode@2.3.8/minified/html5-qrcode.min.js"></script>
    <link
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css"
      rel="stylesheet"
    />

    <!-- Custom Styles -->
    <link
      rel="stylesheet"
      href="{{ url_for('static', filename='css/index.css') }}"
    />
    <link
      rel="stylesheet"
      href="{{ url_for('static', filename='css/button.css') }}"
    />

    <!-- PWA Support -->
    <meta name="theme-color" content="#6366f1" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="default" />
  </head>
  <body>
    <!-- Skip Link for Accessibility -->
    <a href="#main-content" class="skip-link">Skip to main content</a>

    <!-- Screen Reader Announcements -->
    <div id="sr-announcements" class="sr-announcement" aria-live="polite" aria-atomic="true"></div>
    <!-- Settings Modal -->
    <div id="settingsModal" class="modal-overlay hidden">
      <div class="modal-content" style="width: 90%; max-width: 700px; position: relative">
        <div id="activatePanel" class="settings-panel">
          <button class="close-btn" onclick="closeSettingsModal()" aria-label="Close settings">
            <i class="fas fa-times"></i>
          </button>

          <div class="settings-header">
            <div class="flex items-center space-x-3 mb-4">
              <div class="w-12 h-12 bg-gradient-to-br from-primary to-secondary rounded-xl flex items-center justify-center">
                <i class="fas fa-cog text-white text-xl"></i>
              </div>
              <div>
                <h2 class="text-2xl font-bold text-gray-900">System Configuration</h2>
                <p class="text-gray-600">Configure your dataset, template, and printing preferences</p>
              </div>
            </div>
          </div>

        <div class="mb-4">
          <strong>📄 Current Active Dataset:</strong>
          <div class="d-flex align-items-center gap-2 mt-2">
            <span
              id="current-dataset"
              class="text-truncate"
              style="max-width: 100%"
            >
              {{ active_dataset }}
            </span>
            <button
              class="btn btn-sm btn-outline-primary"
              onclick="triggerFile('dataset')"
            >
              Replace
            </button>
          </div>
          <input
            type="file"
            name="dataset_file"
            accept=".csv"
            class="form-control mt-2 d-none"
            id="datasetInput"
          />
        </div>

        <div class="mb-4">
          <strong>🖼️ Current Active Template:</strong>
          <div class="d-flex align-items-center gap-2 mt-2">
            <span
              id="current-template"
              class="text-truncate"
              style="max-width: 100%"
            >
              {{ active_template }}
            </span>
            <button
              class="btn btn-sm btn-outline-primary"
              onclick="triggerFile('template')"
            >
              Replace
            </button>
          </div>
          <input
            type="file"
            name="template_file"
            accept=".png,.jpg,.jpeg,.gif,.bmp,.webp,.tiff"
            class="form-control mt-2 d-none"
            id="templateInput"
            title="Upload any image file - no size restrictions"
          />
          <small class="text-muted mt-1 d-block">
            <i class="fas fa-info-circle text-success"></i>
            <strong>No size restrictions!</strong> Accepts any image size and format (PNG, JPG, GIF, BMP, WebP, TIFF)
          </small>
        </div>

        <div class="mb-4">
          <strong>📝 Paper Size:</strong>
          <div class="paper-size-control mt-2">
            <select id="paperSize" class="form-select">
              <option value="">Please select paper size</option>
              <option value="A4">A4 (210 × 297 mm)</option>
              <option value="Letter">Letter (216 × 279 mm)</option>
              <option value="Legal">Legal (216 × 356 mm)</option>
              <option value="A3">A3 (297 × 420 mm)</option>
              <option value="A5">A5 (148 × 210 mm)</option>
              <option value="A6">A6 (105 × 148 mm)</option>
              <option value="A7">A7 (74 × 100 mm)</option>
              <option value="B5">B5 (182 × 257 mm)</option>
              <option value="B4">B4 (250 × 353 mm)</option>
              <option value="4x6">4 × 6 in (10.16 × 15.24 cm)</option>
              <option value="5x7">5 × 7 in (12.7 × 17.8 cm)</option>
              <option value="5x8">5 × 8 in (12.7 × 20.32 cm)</option>
              <option value="9x13">3.5 × 5 in (8.9 × 12.7 cm)</option>
              <option value="custom">Custom</option>
            </select>
            <div class="paper-size-note">
              Please choose the appropriate paper size for printing
            </div>

            <div id="customSizeFields" style="display: none; margin-top: 10px">
              <input
                type="number"
                id="customWidth"
                placeholder="Width (in)"
                step="0.01"
                class="form-control form-control-sm mb-2"
                style="max-width: 140px"
              />
              <input
                type="number"
                id="customHeight"
                placeholder="Height (in)"
                step="0.01"
                class="form-control form-control-sm"
                style="max-width: 140px"
              />
            </div>
          </div>

          <div class="form-check text-start mt-3">
            <input
              class="form-check-input"
              type="checkbox"
              id="previewBeforePrint"
            />
            <label class="form-check-label" for="previewBeforePrint">
              Show print preview before printing
            </label>
          </div>
        </div>

        <div class="d-flex justify-content-between align-items-center mt-3">
          <div style="font-size: 11px; opacity: 0.7">
            <img
              src="{{ url_for('static', filename='trademark/blurbgicon.png') }}"
              width="16"
            />
            ID System v1.0 © 2025 jLagzn STUDIO
          </div>
          <button class="btn btn-gradient" onclick="submitSettings()">
            <i class="fas fa-check me-1"></i> Confirm Settings
          </button>
        </div>
      </div>
    </div>

    <!-- Main Application -->
    <div class="min-h-screen bg-gradient-to-br from-gray-50 to-blue-50">
      <!-- Header -->
      <header class="bg-white shadow-sm border-b border-gray-200">
        <div class="container mx-auto px-4 py-4">
          <div class="flex items-center justify-between">
            <div class="flex items-center space-x-3">
              <img
                src="{{ url_for('static', filename='trademark/blurbgicon.png') }}"
                alt="Logo"
                class="w-10 h-10 rounded-lg"
              />
              <div>
                <h1 class="text-xl font-bold text-gray-900">Smart QR ID Scanner</h1>
                <p class="text-sm text-gray-500">Professional ID Management System</p>
              </div>
            </div>

            <!-- Header Actions -->
            <div class="flex items-center space-x-4">
              <!-- Dark Mode Toggle -->
              <button
                id="themeToggle"
                class="theme-toggle"
                aria-label="Toggle dark mode"
                title="Toggle dark/light mode"
              >
                <i class="fas fa-sun sun-icon"></i>
                <i class="fas fa-moon moon-icon"></i>
              </button>

              <!-- Settings Button -->
              <button
                class="btn btn-primary btn-sm"
                onclick="openSettingsModal()"
                aria-label="Open settings"
              >
                <i class="fas fa-cog"></i>
                <span class="hidden sm:inline">Settings</span>
              </button>
            </div>
          </div>
        </div>
      </header>

      <!-- Success Alert -->
      {% if success %}
      <div class="container mx-auto px-4 pt-4">
        <div
          id="successAlert"
          class="bg-green-50 border border-green-200 text-green-800 px-4 py-3 rounded-lg flex items-center justify-between"
          role="alert"
        >
          <div class="flex items-center">
            <i class="fas fa-check-circle mr-2"></i>
            <span>Activation successful. Dataset and/or template updated.</span>
          </div>
          <button
            type="button"
            class="text-green-600 hover:text-green-800"
            onclick="this.parentElement.remove()"
            aria-label="Close"
          >
            <i class="fas fa-times"></i>
          </button>
        </div>
      </div>
      {% endif %}

      <!-- Main Content -->
      <main id="main-content" class="container mx-auto px-4 py-8" role="main">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <!-- ID Template Preview -->
          <div class="preview-section" role="region" aria-labelledby="preview-title">
            <div class="section-title" id="preview-title">
              <i class="fas fa-id-card" aria-hidden="true"></i>
              ID Template Preview
            </div>

            <div class="preview-container">
              <div
                id="paperBoundary"
                class="preview-frame"
              >
                <div
                  id="scaledPreviewWrapper"
                  class="preview-wrapper"
                >
                  <div
                    id="idPreviewContainer"
                    class="id-preview-container"
                  >
                    <img
                      id="id-template-preview"
                      src="{{ get_template_preview_url(active_template) }}?v={{ config.get('active_dataset', '') }}"
                      alt="ID Template Preview"
                      class="template-image"
                      onerror="this.onerror=null;this.src='{{ url_for('static', filename='id_templates/default_template.png') }}'"
                    />
                    <div class="id-overlay">
                      <div id="preview-id" class="preview-id">000</div>
                      <div class="preview-spacer"></div>
                      <div id="preview-name" class="preview-name">Name Here</div>
                      <div id="preview-position" class="preview-position">Position</div>
                      <div id="preview-company" class="preview-company">Company</div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Preview Controls -->
              <div class="preview-controls mt-4">
                <div class="flex items-center justify-between text-sm text-gray-600">
                  <span>Live Preview</span>
                  <div class="flex items-center space-x-2">
                    <div class="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                    <span>Ready</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Scanner Section -->
          <div class="scanner-section" role="region" aria-labelledby="scanner-title">
            <div class="section-title" id="scanner-title">
              <i class="fas fa-qrcode" aria-hidden="true"></i>
              Smart QR Scanner
            </div>

            <!-- Alert Box -->
            <div id="alertBox" class="alert-container mb-4 hidden" role="alert"></div>

            <!-- Loading Spinner -->
            <div class="text-center mb-4">
              <div id="loadingSpinner" class="loading-spinner hidden">
                <div class="spinner">
                  <span></span>
                  <span></span>
                  <span></span>
                  <span></span>
                </div>
                <p class="mt-2 text-sm text-gray-600">Processing...</p>
              </div>
            </div>

            <!-- QR Scanner -->
            <div class="scanner-container mb-6">
              <div
                id="reader"
                class="qr-reader"
                role="img"
                aria-label="QR code scanner camera view"
                aria-describedby="scanner-instructions"
              ></div>

              <!-- Screen reader instructions -->
              <div id="scanner-instructions" class="sr-only">
                Point your camera at a QR code to scan employee information.
                The system will automatically detect and process the QR code.
              </div>

              <!-- Scanner Status -->
              <div class="scanner-status mt-3">
                <div class="flex items-center justify-center space-x-2 text-sm">
                  <div id="scannerStatus" class="flex items-center space-x-2">
                    <div class="w-2 h-2 bg-yellow-400 rounded-full animate-pulse"></div>
                    <span class="text-gray-600">Initializing camera...</span>
                  </div>
                </div>
              </div>
            </div>

            <!-- ID Information Card -->
            <div class="id-info-card">
              <div class="section-title">
                <i class="fas fa-user"></i>
                Employee Information
              </div>

              <div class="id-info-content">
                <div class="info-grid">
                  <div class="info-item">
                    <label class="info-label">
                      <i class="fas fa-hashtag"></i>
                      ID Number
                    </label>
                    <span id="id" class="info-value">---</span>
                  </div>

                  <div class="info-item">
                    <label class="info-label">
                      <i class="fas fa-user"></i>
                      Full Name
                    </label>
                    <span id="name" class="info-value">---</span>
                  </div>

                  <div class="info-item">
                    <label class="info-label">
                      <i class="fas fa-briefcase"></i>
                      Position
                    </label>
                    <span id="position" class="info-value">---</span>
                  </div>

                  <div class="info-item">
                    <label class="info-label">
                      <i class="fas fa-building"></i>
                      Company
                    </label>
                    <span id="company" class="info-value">---</span>
                  </div>
                </div>

                <!-- Action Buttons -->
                <div class="action-buttons mt-4 space-y-3">
                  <button id="printBtn" class="btn btn-success btn-lg w-full" disabled>
                    <i class="fas fa-print"></i>
                    Print ID Card
                  </button>

                  <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
                    <button id="downloadQrBtn" class="btn btn-primary" disabled>
                      <i class="fas fa-download"></i>
                      Download QR
                    </button>

                    <button id="emailQrBtn" class="btn btn-secondary" disabled>
                      <i class="fas fa-envelope"></i>
                      Email QR
                    </button>
                  </div>

                  <!-- QR Generation Controls -->
                  <div class="qr-generation-section mt-6 pt-4 border-t border-gray-200">
                    <h4 class="text-sm font-semibold text-gray-700 mb-3">
                      <i class="fas fa-qrcode"></i>
                      QR Code Management
                    </h4>

                    <!-- QR Generation Status -->
                    <div id="qrGenerationStatus" class="mb-4 hidden">
                      <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                        <div class="flex items-center justify-between mb-2">
                          <span class="text-sm font-medium text-blue-800">Generating QR Codes...</span>
                          <span id="qrProgress" class="text-sm text-blue-600">0%</span>
                        </div>
                        <div class="w-full bg-blue-200 rounded-full h-2">
                          <div id="qrProgressBar" class="bg-blue-600 h-2 rounded-full transition-all duration-300" style="width: 0%"></div>
                        </div>
                        <div class="flex justify-between text-xs text-blue-600 mt-2">
                          <span id="qrCurrentEmployee">Starting...</span>
                          <span id="qrETA">Calculating...</span>
                        </div>
                      </div>
                    </div>

                    <!-- QR Generation Controls -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-3 mb-4">
                      <button id="generateAllQRsBtn" class="btn btn-primary">
                        <i class="fas fa-magic"></i>
                        Generate All QRs
                      </button>

                      <button id="stopQRGenerationBtn" class="btn btn-danger hidden">
                        <i class="fas fa-stop"></i>
                        Stop Generation
                      </button>
                    </div>

                    <!-- QR Statistics -->
                    <div id="qrStats" class="grid grid-cols-3 gap-2 text-center text-xs mb-4">
                      <div class="bg-green-50 border border-green-200 rounded p-2">
                        <div id="qrGenerated" class="font-bold text-green-800">0</div>
                        <div class="text-green-600">Generated</div>
                      </div>
                      <div class="bg-red-50 border border-red-200 rounded p-2">
                        <div id="qrFailed" class="font-bold text-red-800">0</div>
                        <div class="text-red-600">Failed</div>
                      </div>
                      <div class="bg-blue-50 border border-blue-200 rounded p-2">
                        <div id="qrRemaining" class="font-bold text-blue-800">0</div>
                        <div class="text-blue-600">Remaining</div>
                      </div>
                    </div>
                  </div>

                  <!-- Bulk Actions -->
                  <div class="bulk-actions mt-6 pt-4 border-t border-gray-200">
                    <h4 class="text-sm font-semibold text-gray-700 mb-3">Bulk Actions</h4>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
                      <button id="emailAllBtn" class="btn btn-gradient">
                        <i class="fas fa-paper-plane"></i>
                        Email All QRs
                      </button>

                      <button id="downloadAllBtn" class="btn btn-secondary">
                        <i class="fas fa-file-archive"></i>
                        Download All
                      </button>
                    </div>

                    <!-- Email Status -->
                    <div id="emailStatus" class="mt-3 hidden">
                      <div class="status-indicator processing">
                        <i class="fas fa-spinner fa-spin"></i>
                        <span>Checking email support...</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <footer
        style="
          text-align: center;
          padding: 20px 10px 10px;
          font-size: 12px;
          border-top: 1px solid #eee;
          margin-top: 30px;
        "
      >
        <img
          src="{{ url_for('static', filename='trademark/blurbgicon.png') }}"
          width="20"
          alt="Logo"
        />
        ID System v1.0 © 2025 jLagzn STUDIO | All rights reserved
      </footer>
    </div>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
    <script>
        // Global variables
        const alertBox = document.getElementById("alertBox");
        const spinner = document.getElementById("loadingSpinner");
        let html5QrCode = null;
        let scannerActive = false;
        let datasetFile = null;
        let templateFile = null;
        let settingsConfigured = false;

        const paperSizeMap = {
          A4: [8.27, 11.69],
          Letter: [8.5, 11],
          Legal: [8.5, 14],
          A3: [11.7, 16.5],
          A5: [5.8, 8.3],
          A6: [4.1, 5.8],
          A7: [2.9, 3.9],
          B5: [7.2, 10.1],
          B4: [9.8, 13.9],
          "4x6": [4, 6],
          "5x7": [5, 7],
          "5x8": [5, 8],
          "9x13": [3.5, 5]
        };

        // Dark Mode Management
        class ThemeManager {
          constructor() {
            this.theme = localStorage.getItem('theme') || 'light';
            this.init();
          }

          init() {
            this.applyTheme();
            this.setupToggle();
          }

          applyTheme() {
            document.documentElement.setAttribute('data-theme', this.theme);
            localStorage.setItem('theme', this.theme);

            // Update meta theme color
            const metaTheme = document.querySelector('meta[name="theme-color"]');
            if (metaTheme) {
              metaTheme.content = this.theme === 'dark' ? '#1f2937' : '#6366f1';
            }
          }

          toggle() {
            this.theme = this.theme === 'light' ? 'dark' : 'light';
            this.applyTheme();

            // Add transition effect
            document.body.style.transition = 'background-color 0.3s ease';
            setTimeout(() => {
              document.body.style.transition = '';
            }, 300);
          }

          setupToggle() {
            const toggle = document.getElementById('themeToggle');
            if (toggle) {
              toggle.addEventListener('click', () => this.toggle());
            }
          }
        }

        // Enhanced UI Manager
        class UIManager {
          constructor() {
            this.alerts = new Map();
            this.init();
          }

          init() {
            this.setupAlerts();
            this.setupAnimations();
          }

          showAlert(message, type = 'info', duration = 5000) {
            const alertId = Date.now().toString();
            const alertContainer = document.getElementById('alertBox');

            if (!alertContainer) return;

            alertContainer.className = `alert-container alert-${type}`;
            alertContainer.innerHTML = `
              <i class="fas fa-${this.getAlertIcon(type)}"></i>
              <span>${message}</span>
              <button onclick="this.parentElement.classList.add('hidden')" class="ml-auto">
                <i class="fas fa-times"></i>
              </button>
            `;

            alertContainer.classList.remove('hidden');

            if (duration > 0) {
              setTimeout(() => {
                alertContainer.classList.add('hidden');
              }, duration);
            }
          }

          getAlertIcon(type) {
            const icons = {
              success: 'check-circle',
              warning: 'exclamation-triangle',
              danger: 'exclamation-circle',
              info: 'info-circle'
            };
            return icons[type] || 'info-circle';
          }

          setupAnimations() {
            // Add entrance animations to cards
            const observer = new IntersectionObserver((entries) => {
              entries.forEach(entry => {
                if (entry.isIntersecting) {
                  entry.target.style.opacity = '1';
                  entry.target.style.transform = 'translateY(0)';
                }
              });
            });

            document.querySelectorAll('.card, .preview-section, .scanner-section').forEach(el => {
              el.style.opacity = '0';
              el.style.transform = 'translateY(20px)';
              el.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
              observer.observe(el);
            });
          }

          setupAlerts() {
            // Auto-hide success alerts
            {% if success %}
            setTimeout(() => {
              const alert = document.getElementById('successAlert');
              if (alert) {
                alert.style.opacity = '0';
                alert.style.transform = 'translateY(-10px)';
                setTimeout(() => alert.remove(), 300);
              }
            }, 5000);
            {% endif %}
          }
        }

        // Accessibility Manager
        class AccessibilityManager {
          constructor() {
            this.announcements = document.getElementById('sr-announcements');
            this.init();
          }

          init() {
            this.setupKeyboardNavigation();
            this.setupFocusManagement();
            this.setupAriaLive();
          }

          announce(message, priority = 'polite') {
            if (this.announcements) {
              this.announcements.setAttribute('aria-live', priority);
              this.announcements.textContent = message;

              // Clear after announcement
              setTimeout(() => {
                this.announcements.textContent = '';
              }, 1000);
            }
          }

          setupKeyboardNavigation() {
            // Detect keyboard usage
            document.addEventListener('keydown', (e) => {
              if (e.key === 'Tab') {
                document.body.classList.add('keyboard-user');
              }
            });

            document.addEventListener('mousedown', () => {
              document.body.classList.remove('keyboard-user');
            });
          }

          setupFocusManagement() {
            // Focus trap for modals
            const modals = document.querySelectorAll('.modal-overlay');
            modals.forEach(modal => {
              modal.addEventListener('keydown', this.trapFocus.bind(this));
            });
          }

          trapFocus(e) {
            if (e.key !== 'Tab') return;

            const modal = e.currentTarget;
            const focusableElements = modal.querySelectorAll(
              'button, input, select, textarea, [tabindex]:not([tabindex="-1"])'
            );

            const firstElement = focusableElements[0];
            const lastElement = focusableElements[focusableElements.length - 1];

            if (e.shiftKey && document.activeElement === firstElement) {
              e.preventDefault();
              lastElement.focus();
            } else if (!e.shiftKey && document.activeElement === lastElement) {
              e.preventDefault();
              firstElement.focus();
            }
          }

          setupAriaLive() {
            // Update ARIA live regions for dynamic content
            const observer = new MutationObserver((mutations) => {
              mutations.forEach((mutation) => {
                if (mutation.type === 'childList' && mutation.target.id === 'alertBox') {
                  const alertBox = mutation.target;
                  if (!alertBox.classList.contains('hidden')) {
                    this.announce(alertBox.textContent);
                  }
                }
              });
            });

            const alertBox = document.getElementById('alertBox');
            if (alertBox) {
              observer.observe(alertBox, { childList: true, subtree: true });
            }
          }

          updateScannerStatus(status) {
            const statusElement = document.getElementById('scannerStatus');
            if (statusElement) {
              statusElement.setAttribute('aria-live', 'polite');
              this.announce(`Scanner status: ${status}`);
            }
          }

          announceEmployeeData(data) {
            const message = `Employee found: ${data.Name}, ${data.Position} at ${data.Company}, ID number ${data.ID}`;
            this.announce(message, 'assertive');
          }
        }

        // Initialize managers
        const themeManager = new ThemeManager();
        const uiManager = new UIManager();
        const accessibilityManager = new AccessibilityManager();

        // Email and Download Manager
        class EmailDownloadManager {
          constructor() {
            this.currentEmployeeId = null;
            this.emailSupport = null;
            this.init();
          }

          init() {
            this.setupEventListeners();
            this.checkEmailSupport();
          }

          setupEventListeners() {
            // Individual actions
            document.getElementById('downloadQrBtn')?.addEventListener('click', () => {
              this.downloadQR(this.currentEmployeeId);
            });

            document.getElementById('emailQrBtn')?.addEventListener('click', () => {
              this.emailQR(this.currentEmployeeId);
            });

            // Bulk actions
            document.getElementById('emailAllBtn')?.addEventListener('click', () => {
              this.emailAllQRs();
            });

            document.getElementById('downloadAllBtn')?.addEventListener('click', () => {
              this.downloadAllQRs();
            });
          }

          async checkEmailSupport() {
            try {
              const response = await fetch('/check_email_support');
              const data = await response.json();

              this.emailSupport = data.data;
              this.updateEmailUI();

            } catch (error) {
              console.error('Failed to check email support:', error);
              this.updateEmailUI(false);
            }
          }

          updateEmailUI(available = null) {
            const emailBtn = document.getElementById('emailQrBtn');
            const emailAllBtn = document.getElementById('emailAllBtn');
            const statusDiv = document.getElementById('emailStatus');

            if (available === false || (this.emailSupport && !this.emailSupport.mail_available)) {
              // Email not available
              if (emailBtn) {
                emailBtn.disabled = true;
                emailBtn.title = 'Email service not configured';
              }
              if (emailAllBtn) {
                emailAllBtn.disabled = true;
                emailAllBtn.innerHTML = '<i class="fas fa-exclamation-triangle"></i> Email Not Available';
              }

              if (statusDiv) {
                statusDiv.innerHTML = `
                  <div class="status-indicator offline">
                    <i class="fas fa-times-circle"></i>
                    <span>Email service not configured</span>
                  </div>
                `;
                statusDiv.classList.remove('hidden');
              }
            } else if (this.emailSupport) {
              // Email available
              const hasEmails = this.emailSupport.has_email_column && this.emailSupport.email_count > 0;

              if (emailAllBtn) {
                if (hasEmails) {
                  emailAllBtn.disabled = false;
                  emailAllBtn.innerHTML = `<i class="fas fa-paper-plane"></i> Email All QRs (${this.emailSupport.email_count})`;
                } else {
                  emailAllBtn.disabled = true;
                  emailAllBtn.innerHTML = '<i class="fas fa-exclamation-triangle"></i> No Email Addresses';
                }
              }

              if (statusDiv) {
                if (hasEmails) {
                  statusDiv.innerHTML = `
                    <div class="status-indicator online">
                      <i class="fas fa-check-circle"></i>
                      <span>${this.emailSupport.email_count} of ${this.emailSupport.total_employees} employees have email addresses</span>
                    </div>
                  `;
                } else {
                  statusDiv.innerHTML = `
                    <div class="status-indicator offline">
                      <i class="fas fa-info-circle"></i>
                      <span>No email addresses found in dataset. Add an 'Email' column to enable email features.</span>
                    </div>
                  `;
                }
                statusDiv.classList.remove('hidden');
              }
            }
          }

          setCurrentEmployee(employeeId, hasEmail = false) {
            this.currentEmployeeId = employeeId;

            // Update button states
            const downloadBtn = document.getElementById('downloadQrBtn');
            const emailBtn = document.getElementById('emailQrBtn');

            if (downloadBtn) {
              downloadBtn.disabled = !employeeId;
            }

            if (emailBtn) {
              emailBtn.disabled = !employeeId || !hasEmail || !this.emailSupport?.mail_available;
              emailBtn.title = !hasEmail ? 'No email address for this employee' :
                              !this.emailSupport?.mail_available ? 'Email service not configured' : '';
            }
          }

          async downloadQR(employeeId) {
            if (!employeeId) return;

            try {
              uiManager.showAlert('Preparing QR download...', 'info', 2000);

              const response = await fetch(`/download_qr/${employeeId}`);

              if (response.ok) {
                const blob = await response.blob();
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `QR_${employeeId}.png`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                window.URL.revokeObjectURL(url);

                uiManager.showAlert('QR code downloaded successfully!', 'success');
              } else {
                const errorData = await response.json();
                uiManager.showAlert(errorData.message || 'Download failed', 'danger');
              }
            } catch (error) {
              console.error('Download failed:', error);
              uiManager.showAlert('Download failed: ' + error.message, 'danger');
            }
          }

          async emailQR(employeeId) {
            if (!employeeId) return;

            try {
              uiManager.showAlert('Sending QR via email...', 'info', 0);

              // For individual email, we'll use the bulk endpoint with a filter
              // This is a simplified approach - you might want a dedicated endpoint
              uiManager.showAlert('Individual email sending not yet implemented. Use "Email All QRs" for now.', 'warning');

            } catch (error) {
              console.error('Email failed:', error);
              uiManager.showAlert('Email failed: ' + error.message, 'danger');
            }
          }

          async emailAllQRs() {
            if (!this.emailSupport?.mail_available) {
              uiManager.showAlert('Email service not configured', 'danger');
              return;
            }

            if (!confirm(`Send QR codes to all employees with email addresses (${this.emailSupport.email_count} recipients)?`)) {
              return;
            }

            try {
              const emailAllBtn = document.getElementById('emailAllBtn');
              const originalText = emailAllBtn.innerHTML;

              emailAllBtn.disabled = true;
              emailAllBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Sending Emails...';

              uiManager.showAlert('Sending QR codes via email...', 'info', 0);

              const response = await fetch('/send_qr_emails', {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json'
                }
              });

              const data = await response.json();

              if (data.success) {
                const summary = data.data.summary;
                uiManager.showAlert(
                  `Email sending completed! Sent: ${summary.sent}, Failed: ${summary.failed}, No Email: ${summary.no_email}`,
                  'success'
                );
              } else {
                uiManager.showAlert(data.message || 'Email sending failed', 'danger');
              }

            } catch (error) {
              console.error('Bulk email failed:', error);
              uiManager.showAlert('Email sending failed: ' + error.message, 'danger');
            } finally {
              const emailAllBtn = document.getElementById('emailAllBtn');
              emailAllBtn.disabled = false;
              emailAllBtn.innerHTML = `<i class="fas fa-paper-plane"></i> Email All QRs`;
            }
          }

          async downloadAllQRs() {
            try {
              uiManager.showAlert('Preparing ZIP download...', 'info', 0);

              const downloadAllBtn = document.getElementById('downloadAllBtn');
              const originalText = downloadAllBtn.innerHTML;

              downloadAllBtn.disabled = true;
              downloadAllBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Creating ZIP...';

              const response = await fetch('/download_all_qrs');

              if (response.ok) {
                const blob = await response.blob();
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `All_QR_Codes_${new Date().toISOString().slice(0,10)}.zip`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                window.URL.revokeObjectURL(url);

                uiManager.showAlert('All QR codes downloaded successfully!', 'success');
              } else {
                const errorData = await response.json();
                uiManager.showAlert(errorData.message || 'Download failed', 'danger');
              }

            } catch (error) {
              console.error('Bulk download failed:', error);
              uiManager.showAlert('Download failed: ' + error.message, 'danger');
            } finally {
              const downloadAllBtn = document.getElementById('downloadAllBtn');
              downloadAllBtn.disabled = false;
              downloadAllBtn.innerHTML = '<i class="fas fa-file-archive"></i> Download All';
            }
          }
        }

        // QR Generation Manager
        class QRGenerationManager {
          constructor() {
            this.statusCheckInterval = null;
            this.isGenerating = false;
            this.init();
          }

          init() {
            this.setupEventListeners();
            this.checkInitialStatus();
          }

          setupEventListeners() {
            document.getElementById('generateAllQRsBtn')?.addEventListener('click', () => {
              this.startGeneration();
            });

            document.getElementById('stopQRGenerationBtn')?.addEventListener('click', () => {
              this.stopGeneration();
            });
          }

          async checkInitialStatus() {
            try {
              const response = await fetch('/qr_generation_status');
              const data = await response.json();

              if (data.success && data.data.active) {
                this.startStatusMonitoring();
                this.updateUI(data.data);
              }
            } catch (error) {
              console.error('Failed to check initial QR status:', error);
            }
          }

          async startGeneration() {
            try {
              const generateBtn = document.getElementById('generateAllQRsBtn');
              const stopBtn = document.getElementById('stopQRGenerationBtn');

              generateBtn.disabled = true;
              generateBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Starting...';

              const response = await fetch('/start_background_qr_generation', {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json'
                }
              });

              const data = await response.json();

              if (data.success) {
                this.isGenerating = true;

                // Check if it's synchronous mode
                if (data.data && data.data.mode === 'synchronous') {
                  uiManager.showAlert('QR generation completed in synchronous mode', 'success');
                  generateBtn.disabled = false;
                  generateBtn.innerHTML = '<i class="fas fa-magic"></i> Generate All QRs';
                } else {
                  // Background mode
                  this.startStatusMonitoring();
                  generateBtn.classList.add('hidden');
                  stopBtn.classList.remove('hidden');
                  document.getElementById('qrGenerationStatus').classList.remove('hidden');
                }

                uiManager.showAlert(data.message, 'success');

              } else {
                uiManager.showAlert(data.message || 'Failed to start QR generation', 'danger');
                generateBtn.disabled = false;
                generateBtn.innerHTML = '<i class="fas fa-magic"></i> Generate All QRs';
              }

            } catch (error) {
              console.error('Failed to start QR generation:', error);
              uiManager.showAlert('Failed to start QR generation: ' + error.message, 'danger');

              const generateBtn = document.getElementById('generateAllQRsBtn');
              generateBtn.disabled = false;
              generateBtn.innerHTML = '<i class="fas fa-magic"></i> Generate All QRs';
            }
          }

          async stopGeneration() {
            try {
              const response = await fetch('/stop_background_qr_generation', {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json'
                }
              });

              const data = await response.json();

              if (data.success) {
                this.stopStatusMonitoring();
                uiManager.showAlert('QR generation stopped', 'info');
              } else {
                uiManager.showAlert('Failed to stop QR generation', 'danger');
              }

            } catch (error) {
              console.error('Failed to stop QR generation:', error);
              uiManager.showAlert('Failed to stop QR generation: ' + error.message, 'danger');
            }
          }

          startStatusMonitoring() {
            if (this.statusCheckInterval) {
              clearInterval(this.statusCheckInterval);
            }

            this.statusCheckInterval = setInterval(() => {
              this.checkStatus();
            }, 2000); // Check every 2 seconds
          }

          stopStatusMonitoring() {
            if (this.statusCheckInterval) {
              clearInterval(this.statusCheckInterval);
              this.statusCheckInterval = null;
            }

            this.isGenerating = false;
            this.resetUI();
          }

          async checkStatus() {
            try {
              const response = await fetch('/qr_generation_status');
              const data = await response.json();

              if (data.success) {
                this.updateUI(data.data);

                // Check if generation is complete
                if (!data.data.active && this.isGenerating) {
                  this.stopStatusMonitoring();
                  uiManager.showAlert(
                    `QR generation completed! Generated: ${data.data.generated}, Failed: ${data.data.failed}`,
                    'success'
                  );
                }
              }

            } catch (error) {
              console.error('Failed to check QR status:', error);
            }
          }

          updateUI(status) {
            // Update progress bar
            const progressBar = document.getElementById('qrProgressBar');
            const progressText = document.getElementById('qrProgress');

            if (progressBar && progressText) {
              progressBar.style.width = `${status.percentage || 0}%`;
              progressText.textContent = `${Math.round(status.percentage || 0)}%`;
            }

            // Update current employee
            const currentEmployee = document.getElementById('qrCurrentEmployee');
            if (currentEmployee) {
              if (status.current_id) {
                currentEmployee.textContent = `Processing ID: ${status.current_id}`;
              } else if (status.active) {
                currentEmployee.textContent = 'Preparing...';
              } else {
                currentEmployee.textContent = 'Idle';
              }
            }

            // Update ETA
            const eta = document.getElementById('qrETA');
            if (eta && status.estimated_completion) {
              const etaDate = new Date(status.estimated_completion);
              const now = new Date();
              const diffMinutes = Math.round((etaDate - now) / 60000);

              if (diffMinutes > 0) {
                eta.textContent = `ETA: ${diffMinutes}m`;
              } else {
                eta.textContent = 'Almost done...';
              }
            } else if (eta) {
              eta.textContent = status.active ? 'Calculating...' : 'Complete';
            }

            // Update statistics
            document.getElementById('qrGenerated').textContent = status.generated || 0;
            document.getElementById('qrFailed').textContent = status.failed || 0;
            document.getElementById('qrRemaining').textContent = Math.max(0, (status.total || 0) - (status.progress || 0));
          }

          resetUI() {
            // Hide status section
            document.getElementById('qrGenerationStatus').classList.add('hidden');

            // Reset buttons
            const generateBtn = document.getElementById('generateAllQRsBtn');
            const stopBtn = document.getElementById('stopQRGenerationBtn');

            generateBtn.classList.remove('hidden');
            generateBtn.disabled = false;
            generateBtn.innerHTML = '<i class="fas fa-magic"></i> Generate All QRs';

            stopBtn.classList.add('hidden');

            // Reset progress
            document.getElementById('qrProgressBar').style.width = '0%';
            document.getElementById('qrProgress').textContent = '0%';
          }
        }

        // Initialize managers
        const themeManager = new ThemeManager();
        const uiManager = new UIManager();
        const accessibilityManager = new AccessibilityManager();
        const emailDownloadManager = new EmailDownloadManager();
        const qrGenerationManager = new QRGenerationManager();

        // Initialize on DOM load
        document.addEventListener("DOMContentLoaded", function() {
          // Show settings modal on first load
          if (!settingsConfigured) {
            document.getElementById("settingsModal").classList.remove("hidden");
          }

          // Set up paper size controls
          setupPaperSizeControls();

          // Initialize camera with better error handling
          initCameraWithRetry().catch(err => {
            uiManager.showAlert("Camera initialization failed: " + err.message, "danger");
          });
        });

        // Enhanced Modal functions
        function openSettingsModal() {
          const modal = document.getElementById("settingsModal");
          modal.classList.remove("hidden");
          document.body.style.overflow = "hidden";

          // Focus management for accessibility
          const firstFocusable = modal.querySelector('button, input, select');
          if (firstFocusable) firstFocusable.focus();
        }

        function closeSettingsModal() {
          const modal = document.getElementById("settingsModal");
          modal.classList.add("hidden");
          document.body.style.overflow = "";
        }

        // Enhanced keyboard navigation
        document.addEventListener('keydown', function(e) {
          const modal = document.getElementById("settingsModal");

          if (!modal.classList.contains("hidden")) {
            if (e.key === 'Escape') {
              closeSettingsModal();
            }

            // Trap focus within modal
            if (e.key === 'Tab') {
              const focusableElements = modal.querySelectorAll(
                'button, input, select, textarea, [tabindex]:not([tabindex="-1"])'
              );
              const firstElement = focusableElements[0];
              const lastElement = focusableElements[focusableElements.length - 1];

              if (e.shiftKey && document.activeElement === firstElement) {
                e.preventDefault();
                lastElement.focus();
              } else if (!e.shiftKey && document.activeElement === lastElement) {
                e.preventDefault();
                firstElement.focus();
              }
            }
          }
        });

        function submitSettings() {
          const paperSize = document.getElementById("paperSize").value;
          if (!paperSize) {
            showAlert("Please select a paper size", "warning");
            return;
          }

          settingsConfigured = true;
          closeSettingsModal();
          initCamera();
        }

        // Camera functions
        function initCamera() {
          if (html5QrCode) return;

          html5QrCode = new Html5Qrcode("reader");
          startCamera();
        }

        function startCamera() {
          if (scannerActive || !settingsConfigured) return;

          scannerActive = true;
          html5QrCode.start(
            { facingMode: "environment" },
            { fps: 10, qrbox: 250 },
            (decodedText) => {
              scannerActive = false;
              html5QrCode.stop().then(() => {
                fetchData(decodedText.trim());
              });
            },
            (error) => {
              scannerActive = false;
              console.error("QR Scanner error:", error);
            }
          ).catch((err) => {
            scannerActive = false;
            showAlert("Camera error: " + err.message, "danger");
          });
        }

        // Data handling
        function clearFields() {
          ["id", "name", "position", "company"].forEach(
            (id) => (document.getElementById(id).textContent = "")
          );
        }

        function fetchData(qrContent) {
          clearFields();
          spinner.classList.remove("d-none");

          fetch("/get_data", {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({ qr_content: qrContent }),
          })
            .then((res) => res.json())
            .then((data) => {
              spinner.classList.add("d-none");

              if (data.error) {
                showAlert("❌ Record not found or invalid QR code", "danger");
                resetSystem();
                return;
              }

              // Update display fields
              ["ID", "Name", "Position", "Company"].forEach((field) => {
                document.getElementById(field.toLowerCase()).textContent = data[field];
              });

              // Update preview
              document.getElementById("preview-id").textContent = String(data["ID"]).padStart(3, "0");
              document.getElementById("preview-name").textContent = data["Name"];
              document.getElementById("preview-position").textContent = data["Position"];
              document.getElementById("preview-company").textContent = data["Company"];

              // Update email/download buttons
              const hasEmail = data.Email && data.Email.trim() && data.Email.toLowerCase() !== 'nan';
              emailDownloadManager.setCurrentEmployee(data.ID, hasEmail);

              // Announce to screen readers
              if (accessibilityManager) {
                accessibilityManager.announceEmployeeData(data);
              }

              // Auto print
              printAndRestart();
            })
            .catch((err) => {
              spinner.classList.add("d-none");
              showAlert("Fetch error: " + err.message, "danger");
              resetSystem();
            });
        }

        // Printing functions
        function printAndRestart() {
          const preview = document.getElementById("idPreviewContainer");
          const wrapper = document.getElementById("scaledPreviewWrapper");
          const paperSize = document.getElementById("paperSize").value;
          const customWidth = parseFloat(document.getElementById("customWidth").value) || 3.375;
          const customHeight = parseFloat(document.getElementById("customHeight").value) || 2.125;
          const showPreview = document.getElementById("previewBeforePrint").checked;

          const TARGET_DPI = 300;
          const SCREEN_PPI = 96;
          const scale = TARGET_DPI / SCREEN_PPI;

          const originalTransform = wrapper.style.transform;
          wrapper.style.transform = "scale(1)";

          html2canvas(preview, {
              scale: scale,
              useCORS: true,
              allowTaint: true,
              backgroundColor: null,
          }).then((canvas) => {
              wrapper.style.transform = originalTransform;

              const base64Image = canvas.toDataURL("image/png");

              const payload = {
                  image_base64: base64Image,
                  paper_size: paperSize,
                  custom_width: customWidth,
                  custom_height: customHeight,
                  from_preview: window.location.pathname.includes('show_preview')
              };

              if (paperSize !== "custom") {
                  delete payload.custom_width;
                  delete payload.custom_height;
              }

              if (showPreview && !payload.from_preview) {
                  payload.redirect = true;
              }

              fetch("/print_image_direct", {
                  method: "POST",
                  headers: { "Content-Type": "application/json" },
                  body: JSON.stringify(payload),
              })
              .then((res) => res.json())
              .then((result) => {
                  if (result.success) {
                      if (result.preview_url && !payload.from_preview) {
                          window.location.href = result.preview_url;
                      } else {
                          setTimeout(() => resetSystem(), 1000);
                      }
                  } else {
                      showAlert("❌ Failed to print: " + (result.error || "Unknown error"), "danger");
                      resetSystem();
                  }
              })
              .catch((err) => {
                  showAlert("❌ Network error: " + err.message, "danger");
                  resetSystem();
              });
          });
      }

        function resetSystem() {
          clearFields();
          startCamera();
        }

        // Settings panel functions
        function triggerFile(type) {
          document.getElementById(`${type}Input`).click();
        }

        function submitActivation() {
          if (!datasetFile && !templateFile) {
            showAlert("Please select at least a dataset or template file.", "warning");
            return;
          }

          const formData = new FormData();
          if (datasetFile) formData.append("dataset_file", datasetFile);
          if (templateFile) formData.append("template_file", templateFile);
          formData.append("triggeredBy", "manual");

          fetch("/activate", {
            method: "POST",
            body: formData,
          })
            .then((res) => {
              if (res.redirected) {
                window.location.href = res.url;
              } else {
                showAlert("Activation failed.", "danger");
              }
            })
            .catch((err) => {
              showAlert("Error during activation: " + err.message, "danger");
            });
        }

        // Paper size functions
        function setupPaperSizeControls() {
          const paperSelect = document.getElementById("paperSize");
          const customFields = document.getElementById("customSizeFields");

          paperSelect.addEventListener("change", function() {
            if (this.value === "custom") {
              customFields.style.display = "block";
              return;
            }
            customFields.style.display = "none";
            setPaperSize(this.value);
          });

          document.getElementById("customWidth").addEventListener("input", applyCustomSize);
          document.getElementById("customHeight").addEventListener("input", applyCustomSize);

          // Set default size if one is already selected
          if (paperSelect.value) {
            setPaperSize(paperSelect.value);
          }
        }

        function setPaperSize(size) {
          if (size === "custom") return;

          const [width, height] = paperSizeMap[size] || paperSizeMap["A4"];
          updatePreviewSize(width, height);
        }

        function applyCustomSize() {
          const width = parseFloat(document.getElementById("customWidth").value);
          const height = parseFloat(document.getElementById("customHeight").value);

          if (width && height) {
            updatePreviewSize(width, height);
          }
        }

        function updatePreviewSize(widthInInches, heightInInches) {
          const DPI = 96;
          const MARGIN_MM = 1;
          const MARGIN_IN = MARGIN_MM / 25.4;
          const maxFramePx = 500;
          const paddingPercent = 0.05;

          const widthPx = (widthInInches - 2 * MARGIN_IN) * DPI;
          const heightPx = (heightInInches - 2 * MARGIN_IN) * DPI;

          const availableWidth = maxFramePx * (1 - 2 * paddingPercent);
          const availableHeight = maxFramePx * (1 - 2 * paddingPercent);
          const scale = Math.min(availableWidth / widthPx, availableHeight / heightPx);

          const preview = document.getElementById("idPreviewContainer");
          preview.style.width = `${widthPx}px`;
          preview.style.height = `${heightPx}px`;

          const wrapper = document.getElementById("scaledPreviewWrapper");
          wrapper.style.transform = `scale(${scale})`;

          // Font scaling
          const baseFontSizePx = 16;
          const referenceHeightInches = 5.8;
          const fontScale = heightInInches / referenceHeightInches;

          document.getElementById("preview-id").style.fontSize = `${Math.round(baseFontSizePx * 1.8 * fontScale)}px`;
          document.getElementById("preview-name").style.fontSize = `${Math.round(baseFontSizePx * 1.4 * fontScale)}px`;
          document.getElementById("preview-position").style.fontSize = `${Math.round(baseFontSizePx * 1.1 * fontScale)}px`;
          document.getElementById("preview-company").style.fontSize = `${Math.round(baseFontSizePx * 1.3 * fontScale)}px`;
        }

        // Helper functions
        function showAlert(message, type) {
          alertBox.className = `alert alert-${type}`;
          alertBox.textContent = message;
          alertBox.classList.remove("d-none");
          setTimeout(() => alertBox.classList.add("d-none"), 3000);
        }

        // File input handlers
        document.getElementById("datasetInput").addEventListener("change", function(e) {
          datasetFile = e.target.files[0];
          document.getElementById("current-dataset").textContent = datasetFile.name;
        });

        document.getElementById("templateInput").addEventListener("change", function(e) {
          templateFile = e.target.files[0];
          document.getElementById("current-template").textContent = templateFile.name;
        });

        // BLUR EFFECT IMPLEMENTATION (add this instead)
        document.getElementById('id-template-preview').onload = function() {
          const img = this;
          const paperBoundary = document.getElementById('paperBoundary');

          // Create blurred background
          const canvas = document.createElement('canvas');
          const ctx = canvas.getContext('2d');
          canvas.width = img.naturalWidth;
          canvas.height = img.naturalHeight;
          ctx.filter = 'blur(20px) brightness(1.1)';
          ctx.drawImage(img, 0, 0, canvas.width, canvas.height);

          // Apply as background
          paperBoundary.style.backgroundImage = `url(${canvas.toDataURL()})`;
          paperBoundary.style.backgroundSize = 'cover';
        };

        // Keep error fallback (but simplified)
        document.getElementById('id-template-preview').onerror = function() {
          this.src = '{{ url_for("static", filename="id_templates/default_template.png") }}?' + Date.now();
        };

        // Add this to your index.html
        async function waitForUploadReady() {
            while (true) {
                const response = await fetch('/upload_status');
                const status = await response.json();
                if (status.ready) return true;
                await new Promise(resolve => setTimeout(resolve, 1000));
            }
        }

        // Modify your upload handler
        async function handleUpload() {
            const uploadBtn = document.getElementById('uploadBtn');
            uploadBtn.disabled = true;
            uploadBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Checking system...';

            await waitForUploadReady();

            uploadBtn.innerHTML = '<i class="fas fa-upload"></i> Upload Files';
            uploadBtn.disabled = false;

            // Proceed with normal upload flow
            document.getElementById('fileInput').click();
        }
         // Enhanced camera initialization with retry logic
        async function initCameraWithRetry(retries = 3, delay = 1000) {
          try {
            await initCamera();
          } catch (err) {
            if (retries > 0) {
              showAlert(`Camera error (${retries} retries left). Trying again...`, "warning");
              await new Promise(resolve => setTimeout(resolve, delay));
              return initCameraWithRetry(retries - 1, delay * 1.5);
            }
            throw err;
          }
        }

        // Enhanced QR code scanning with timeout
        function startCameraWithTimeout(timeout = 30000) {
          const timer = setTimeout(() => {
            if (scannerActive) {
              showAlert("Camera timed out. Please try again.", "warning");
              stopCamera();
            }
          }, timeout);

          startCamera().finally(() => clearTimeout(timer));
        }

        // Add this to your existing initialization
        document.addEventListener("DOMContentLoaded", function() {
          // ... existing code ...

          // Replace initCamera() with:
          initCameraWithRetry().catch(err => {
            showAlert("Failed to initialize camera: " + err.message, "danger");
            console.error("Camera initialization failed:", err);
          });
        });

        // Add offline detection
        window.addEventListener('offline', () => {
          showAlert("You are offline. Some features may not work.", "warning");
        });

        window.addEventListener('online', () => {
          showAlert("Connection restored.", "success");
        });
    </script>
  </body>
</html>
