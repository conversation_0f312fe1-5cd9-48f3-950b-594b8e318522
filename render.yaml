services:
  - type: web
    name: id-qr-printing
    runtime: python
    pythonVersion: "3.10.8"
    buildCommand: |
      pip install --upgrade pip --no-cache-dir
      pip install -r requirements.txt --no-cache-dir --prefer-binary
      python deploy_config.py
      echo "Build completed successfully"
    startCommand: "python startup.py"
    envVars:
      - key: SECRET_KEY
        generateValue: true
      - key: QR_KEY
        generateValue: true
      - key: BASE_DIR
        value: /opt/render/project/src
      - key: FLASK_ENV
        value: production
      - key: MKL_NUM_THREADS
        value: "1"
      - key: OMP_NUM_THREADS
        value: "1"
      - key: OPENBLAS_NUM_THREADS
        value: "1"
      - key: NUMEXPR_NUM_THREADS
        value: "1"
      - key: MEMORY_LIMIT_MB
        value: "150"
      - key: ENABLE_SCHEDULER
        value: "false"
      - key: PYTHONUNBUFFERED
        value: "1"
      - key: PYTHONDONTWRITEBYTECODE
        value: "1"
      - key: MALL<PERSON>_ARENA_MAX
        value: "2"
      # Optional email configuration (uncomment and configure if needed)
      # - key: MAIL_SERVER
      #   value: "smtp.gmail.com"
      # - key: MAIL_PORT
      #   value: "587"
      # - key: MAIL_USE_TLS
      #   value: "true"
      # - key: MAIL_USERNAME
      #   value: "<EMAIL>"
      # - key: MAIL_PASSWORD
      #   value: "your-app-password"
    plan: starter
    autoDeploy: true
    healthCheckPath: /health
    disk:
      name: data_volume
      mountPath: /opt/render/project/src/data
      sizeGB: 1
