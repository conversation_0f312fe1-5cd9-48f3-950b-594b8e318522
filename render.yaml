services:
  - type: web
    name: id-qr-printing
    runtime: python
    pythonVersion: "3.10.8"
    buildCommand: |
      pip install --upgrade pip --no-cache-dir
      pip install -r requirements.txt --no-cache-dir --prefer-binary
      mkdir -p data/{participant_list,id_templates,qr_codes}
      [ -f data/active_config.json ] || echo '{}' > data/active_config.json
      mkdir -p static/id_templates
      [ -f static/id_templates/default_template.png ] || touch static/id_templates/default_template.png
      touch data/.ready
      python -c "import gc; gc.collect()"
    startCommand: "gunicorn --workers 1 --threads 1 --timeout 180 --worker-class sync --max-requests 100 --max-requests-jitter 10 --preload --bind :$PORT app:app"
    envVars:
      - key: SECRET_KEY
        generateValue: true
      - key: QR_KEY
        generateValue: true
      - key: BASE_DIR
        value: /opt/render/project/src
      - key: FLASK_ENV
        value: production
      - key: MKL_NUM_THREADS
        value: "1"
      - key: OMP_NUM_THREADS
        value: "1"
      - key: OPENBLAS_NUM_THREADS
        value: "1"
      - key: NUMEXPR_NUM_THREADS
        value: "1"
      - key: MEMORY_LIMIT_MB
        value: "180"
      - key: GUNICORN_WORKERS
        value: "1"
      - key: GUNICORN_THREADS
        value: "1"
      - key: PYTHONUNBUFFERED
        value: "1"
      - key: PYTHONDONTWRITEBYTECODE
        value: "1"
      - key: MALLOC_ARENA_MAX
        value: "2"
    plan: starter
    autoDeploy: true
    healthCheckPath: /health
    disk:
      name: data_volume
      mountPath: /opt/render/project/src/data
      sizeGB: 1
