### Core Web Framework
Flask==3.0.3
Werkzeug==3.0.3

### Data Processing (Memory-optimized versions)
numpy==1.24.4
pandas==2.0.3

### Image & Document Generation
Pillow==10.3.0
qrcode[pil]==7.4.2
reportlab==4.2.0

### Platform-Specific Printing
pywin32==306; sys_platform == 'win32'

### Security & Validation
cryptography==42.0.7
jsonschema==4.22.0

### Utilities
python-dateutil==2.9.0
python-dotenv==1.0.1
APScheduler==3.10.4
tzdata==2024.1; sys_platform == 'win32'

### Memory Management & System Monitoring
psutil==5.9.8

### Email Support
Flask-Mail==0.9.1

### Production Deployment
gunicorn==22.0.0