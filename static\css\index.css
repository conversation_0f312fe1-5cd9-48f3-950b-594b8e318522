/* Modern CSS Variables with Dark Mode Support */
:root {
    /* Light Mode Colors */
    --primary: #6366f1;
    --primary-dark: #4f46e5;
    --primary-light: #a5b4fc;
    --secondary: #f59e0b;
    --success: #10b981;
    --warning: #f59e0b;
    --danger: #ef4444;
    --info: #3b82f6;

    /* Neutral Colors */
    --white: #ffffff;
    --gray-50: #f9fafb;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-300: #d1d5db;
    --gray-400: #9ca3af;
    --gray-500: #6b7280;
    --gray-600: #4b5563;
    --gray-700: #374151;
    --gray-800: #1f2937;
    --gray-900: #111827;

    /* Semantic Colors */
    --background: var(--white);
    --surface: var(--white);
    --surface-variant: var(--gray-50);
    --text-primary: var(--gray-900);
    --text-secondary: var(--gray-600);
    --text-muted: var(--gray-500);
    --border: var(--gray-200);
    --border-light: var(--gray-100);

    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);

    /* Spacing */
    --space-1: 0.25rem;
    --space-2: 0.5rem;
    --space-3: 0.75rem;
    --space-4: 1rem;
    --space-5: 1.25rem;
    --space-6: 1.5rem;
    --space-8: 2rem;
    --space-10: 2.5rem;
    --space-12: 3rem;
    --space-16: 4rem;

    /* Border Radius */
    --radius-sm: 0.375rem;
    --radius: 0.5rem;
    --radius-md: 0.75rem;
    --radius-lg: 1rem;
    --radius-xl: 1.5rem;
    --radius-2xl: 2rem;
    --radius-full: 9999px;

    /* Transitions */
    --transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-slow: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Dark Mode Variables */
[data-theme="dark"] {
    --background: var(--gray-900);
    --surface: var(--gray-800);
    --surface-variant: var(--gray-700);
    --text-primary: var(--gray-100);
    --text-secondary: var(--gray-300);
    --text-muted: var(--gray-400);
    --border: var(--gray-700);
    --border-light: var(--gray-600);

    /* Dark mode specific colors */
    --primary: #818cf8;
    --primary-dark: #6366f1;
    --primary-light: #c7d2fe;
    --success: #34d399;
    --warning: #fbbf24;
    --danger: #f87171;
    --info: #60a5fa;
}

/* Dark Mode Component Overrides */
[data-theme="dark"] .header {
    background: linear-gradient(135deg, var(--gray-800), var(--gray-700));
    border: 1px solid var(--border);
}

[data-theme="dark"] .preview-frame {
    background: var(--gray-800);
    border-color: var(--border);
}

[data-theme="dark"] .qr-reader {
    background: var(--gray-800);
    border-color: var(--border);
}

[data-theme="dark"] .alert-success {
    background: rgba(52, 211, 153, 0.1);
    border-color: rgba(52, 211, 153, 0.3);
    color: var(--success);
}

[data-theme="dark"] .alert-warning {
    background: rgba(251, 191, 36, 0.1);
    border-color: rgba(251, 191, 36, 0.3);
    color: var(--warning);
}

[data-theme="dark"] .alert-danger {
    background: rgba(248, 113, 113, 0.1);
    border-color: rgba(248, 113, 113, 0.3);
    color: var(--danger);
}

[data-theme="dark"] .alert-info {
    background: rgba(96, 165, 250, 0.1);
    border-color: rgba(96, 165, 250, 0.3);
    color: var(--info);
}

/* Theme Toggle Button */
.theme-toggle {
    position: relative;
    width: 60px;
    height: 30px;
    background: var(--surface-variant);
    border-radius: var(--radius-full);
    border: 2px solid var(--border);
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    padding: 2px;
}

.theme-toggle::before {
    content: '';
    position: absolute;
    width: 22px;
    height: 22px;
    background: var(--white);
    border-radius: 50%;
    transition: var(--transition);
    box-shadow: var(--shadow-sm);
    left: 2px;
}

[data-theme="dark"] .theme-toggle::before {
    transform: translateX(28px);
    background: var(--gray-200);
}

.theme-toggle:hover {
    background: var(--primary-light);
    border-color: var(--primary);
}

/* Theme Toggle Icons */
.theme-toggle .sun-icon,
.theme-toggle .moon-icon {
    position: absolute;
    font-size: 0.75rem;
    transition: var(--transition);
    z-index: 1;
}

.theme-toggle .sun-icon {
    left: 6px;
    color: var(--warning);
    opacity: 1;
}

.theme-toggle .moon-icon {
    right: 6px;
    color: var(--primary);
    opacity: 0.5;
}

[data-theme="dark"] .theme-toggle .sun-icon {
    opacity: 0.5;
}

[data-theme="dark"] .theme-toggle .moon-icon {
    opacity: 1;
    color: var(--primary-light);
}

/* Settings Panel Styles */
.settings-panel {
    padding: var(--space-8);
    background: var(--surface);
    border-radius: var(--radius-2xl);
    position: relative;
}

.settings-header {
    border-bottom: 1px solid var(--border);
    padding-bottom: var(--space-6);
    margin-bottom: var(--space-6);
}

.settings-section {
    margin-bottom: var(--space-6);
    padding: var(--space-4);
    background: var(--surface-variant);
    border-radius: var(--radius-lg);
    border: 1px solid var(--border-light);
}

.settings-section h3 {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--space-3);
}

.settings-section h3 i {
    color: var(--primary);
}

.file-upload-area {
    border: 2px dashed var(--border);
    border-radius: var(--radius-lg);
    padding: var(--space-6);
    text-align: center;
    background: var(--surface);
    transition: var(--transition);
    cursor: pointer;
}

.file-upload-area:hover {
    border-color: var(--primary);
    background: rgba(99, 102, 241, 0.05);
}

.file-upload-area.dragover {
    border-color: var(--primary);
    background: rgba(99, 102, 241, 0.1);
    transform: scale(1.02);
}

.file-info {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--space-3);
    background: var(--surface);
    border-radius: var(--radius);
    border: 1px solid var(--border);
    margin-top: var(--space-3);
}

.file-info .file-name {
    font-family: monospace;
    font-size: 0.875rem;
    color: var(--text-secondary);
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.form-group {
    margin-bottom: var(--space-4);
}

.form-label {
    display: block;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--space-2);
    font-size: 0.875rem;
}

.form-select,
.form-input {
    width: 100%;
    padding: var(--space-3);
    border: 1px solid var(--border);
    border-radius: var(--radius);
    background: var(--surface);
    color: var(--text-primary);
    font-size: 0.875rem;
    transition: var(--transition);
}

.form-select:focus,
.form-input:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.form-checkbox {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    margin-top: var(--space-3);
}

.form-checkbox input[type="checkbox"] {
    width: 18px;
    height: 18px;
    accent-color: var(--primary);
}

.custom-size-inputs {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--space-3);
    margin-top: var(--space-3);
    padding: var(--space-4);
    background: var(--surface);
    border-radius: var(--radius);
    border: 1px solid var(--border-light);
}

.settings-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: var(--space-6);
    border-top: 1px solid var(--border);
}

.settings-footer .copyright {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    font-size: 0.75rem;
    color: var(--text-muted);
}

.settings-footer .copyright img {
    width: 16px;
    height: 16px;
    opacity: 0.7;
}

/* Enhanced Form Styles */
.input-group {
    position: relative;
}

.input-group .form-input {
    padding-left: var(--space-10);
}

.input-group .input-icon {
    position: absolute;
    left: var(--space-3);
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-muted);
    font-size: 0.875rem;
}

/* Status Indicators */
.status-indicator {
    display: inline-flex;
    align-items: center;
    gap: var(--space-2);
    padding: var(--space-1) var(--space-2);
    border-radius: var(--radius-full);
    font-size: 0.75rem;
    font-weight: 500;
}

.status-indicator.online {
    background: rgba(52, 211, 153, 0.1);
    color: var(--success);
}

.status-indicator.offline {
    background: rgba(248, 113, 113, 0.1);
    color: var(--danger);
}

.status-indicator.processing {
    background: rgba(251, 191, 36, 0.1);
    color: var(--warning);
}

/* Enhanced Animations */
@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInDown {
    from {
        opacity: 0;
        transform: translateY(-30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.animate-slide-up {
    animation: slideInUp 0.4s ease-out;
}

.animate-slide-down {
    animation: slideInDown 0.4s ease-out;
}

/* Accessibility Utilities */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Focus Styles */
*:focus {
    outline: 2px solid var(--primary);
    outline-offset: 2px;
}

.btn:focus {
    outline: 2px solid var(--primary);
    outline-offset: 2px;
    box-shadow: 0 0 0 4px rgba(99, 102, 241, 0.2);
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
    :root {
        --border: #000000;
        --text-primary: #000000;
        --text-secondary: #000000;
    }

    [data-theme="dark"] {
        --border: #ffffff;
        --text-primary: #ffffff;
        --text-secondary: #ffffff;
    }

    .btn {
        border-width: 2px;
    }

    .card,
    .preview-section,
    .scanner-section {
        border-width: 2px;
    }
}

/* Skip Link */
.skip-link {
    position: absolute;
    top: -40px;
    left: 6px;
    background: var(--primary);
    color: var(--white);
    padding: 8px;
    text-decoration: none;
    border-radius: var(--radius);
    z-index: 1000;
    transition: var(--transition);
}

.skip-link:focus {
    top: 6px;
}

/* Keyboard Navigation Indicators */
.keyboard-user *:focus {
    outline: 2px solid var(--primary);
    outline-offset: 2px;
}

/* Touch Device Optimizations */
@media (pointer: coarse) {
    .btn,
    .close-btn,
    .theme-toggle {
        min-height: 44px;
        min-width: 44px;
    }

    .info-item {
        min-height: 44px;
        padding: var(--space-4);
    }
}

/* Screen Reader Announcements */
.sr-announcement {
    position: absolute;
    left: -10000px;
    width: 1px;
    height: 1px;
    overflow: hidden;
}

/* Loading States for Screen Readers */
[aria-busy="true"] {
    cursor: wait;
}

/* Error States */
.error-state {
    border-color: var(--danger);
    background: rgba(239, 68, 68, 0.05);
}

.error-message {
    color: var(--danger);
    font-size: 0.875rem;
    margin-top: var(--space-1);
    display: flex;
    align-items: center;
    gap: var(--space-1);
}

/* Success States */
.success-state {
    border-color: var(--success);
    background: rgba(16, 185, 129, 0.05);
}

/* Focus Management */
.modal-overlay:focus-within {
    outline: none;
}

.modal-content {
    outline: none;
}

/* Reduced Motion Accessibility */
@media (prefers-reduced-motion: reduce) {
    .animate-slide-up,
    .animate-slide-down,
    .animate-pulse {
        animation: none;
    }

    * {
        transition-duration: 0.01ms !important;
        animation-duration: 0.01ms !important;
    }
}

/* Email and Download Features */
.bulk-actions {
    background: var(--surface-variant);
    border-radius: var(--radius-lg);
    padding: var(--space-4);
    border: 1px solid var(--border-light);
}

.bulk-actions h4 {
    color: var(--text-primary);
    margin: 0 0 var(--space-3) 0;
    font-size: 0.875rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.action-buttons {
    display: flex;
    flex-direction: column;
    gap: var(--space-3);
}

.action-buttons .grid {
    display: grid;
    gap: var(--space-3);
}

/* Email Status Indicators */
.status-indicator {
    display: inline-flex;
    align-items: center;
    gap: var(--space-2);
    padding: var(--space-2) var(--space-3);
    border-radius: var(--radius-full);
    font-size: 0.75rem;
    font-weight: 500;
    border: 1px solid;
}

.status-indicator.online {
    background: rgba(16, 185, 129, 0.1);
    border-color: rgba(16, 185, 129, 0.3);
    color: var(--success);
}

.status-indicator.offline {
    background: rgba(248, 113, 113, 0.1);
    border-color: rgba(248, 113, 113, 0.3);
    color: var(--danger);
}

.status-indicator.processing {
    background: rgba(251, 191, 36, 0.1);
    border-color: rgba(251, 191, 36, 0.3);
    color: var(--warning);
}

/* Button Loading States */
.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    pointer-events: none;
}

.btn .fa-spinner {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* Enhanced Grid Utilities */
.space-y-3 > * + * {
    margin-top: var(--space-3);
}

/* Dark Mode Enhancements for New Components */
[data-theme="dark"] .bulk-actions {
    background: var(--gray-700);
    border-color: var(--border);
}

[data-theme="dark"] .status-indicator.online {
    background: rgba(52, 211, 153, 0.15);
    border-color: rgba(52, 211, 153, 0.4);
}

[data-theme="dark"] .status-indicator.offline {
    background: rgba(248, 113, 113, 0.15);
    border-color: rgba(248, 113, 113, 0.4);
}

[data-theme="dark"] .status-indicator.processing {
    background: rgba(251, 191, 36, 0.15);
    border-color: rgba(251, 191, 36, 0.4);
}

/* QR Generation UI Components */
.qr-generation-section {
    background: var(--surface-variant);
    border-radius: var(--radius-lg);
    padding: var(--space-4);
    border: 1px solid var(--border-light);
}

.qr-generation-section h4 {
    color: var(--text-primary);
    margin: 0 0 var(--space-3) 0;
    font-size: 0.875rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    display: flex;
    align-items: center;
    gap: var(--space-2);
}

.qr-generation-section h4 i {
    color: var(--primary);
}

/* Progress Bar Styles */
.progress-container {
    background: var(--surface);
    border-radius: var(--radius);
    padding: var(--space-4);
    border: 1px solid var(--border);
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: var(--gray-200);
    border-radius: var(--radius-full);
    overflow: hidden;
}

.progress-bar-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--primary), var(--primary-light));
    border-radius: var(--radius-full);
    transition: width 0.3s ease;
    position: relative;
}

.progress-bar-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    animation: shimmer 2s infinite;
}

/* QR Statistics Grid */
.qr-stats-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--space-2);
}

.qr-stat-card {
    text-align: center;
    padding: var(--space-3);
    border-radius: var(--radius);
    border: 1px solid;
    transition: var(--transition);
}

.qr-stat-card:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-sm);
}

.qr-stat-value {
    font-size: 1.25rem;
    font-weight: 700;
    line-height: 1;
    margin-bottom: var(--space-1);
}

.qr-stat-label {
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

/* Status Cards */
.status-card {
    background: var(--surface);
    border-radius: var(--radius-lg);
    padding: var(--space-4);
    border: 1px solid var(--border);
    margin-bottom: var(--space-4);
}

.status-card.processing {
    background: rgba(59, 130, 246, 0.05);
    border-color: rgba(59, 130, 246, 0.2);
}

.status-card.success {
    background: rgba(16, 185, 129, 0.05);
    border-color: rgba(16, 185, 129, 0.2);
}

.status-card.error {
    background: rgba(248, 113, 113, 0.05);
    border-color: rgba(248, 113, 113, 0.2);
}

/* Generation Controls */
.generation-controls {
    display: flex;
    gap: var(--space-3);
    align-items: center;
    flex-wrap: wrap;
}

.generation-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.875rem;
    margin-bottom: var(--space-2);
}

.generation-details {
    display: flex;
    justify-content: space-between;
    font-size: 0.75rem;
    color: var(--text-secondary);
    margin-top: var(--space-2);
}

/* Dark Mode for QR Generation Components */
[data-theme="dark"] .qr-generation-section {
    background: var(--gray-700);
    border-color: var(--border);
}

[data-theme="dark"] .progress-container {
    background: var(--gray-800);
    border-color: var(--border);
}

[data-theme="dark"] .progress-bar {
    background: var(--gray-600);
}

[data-theme="dark"] .status-card {
    background: var(--gray-800);
    border-color: var(--border);
}

[data-theme="dark"] .status-card.processing {
    background: rgba(96, 165, 250, 0.1);
    border-color: rgba(96, 165, 250, 0.3);
}

[data-theme="dark"] .status-card.success {
    background: rgba(52, 211, 153, 0.1);
    border-color: rgba(52, 211, 153, 0.3);
}

[data-theme="dark"] .status-card.error {
    background: rgba(248, 113, 113, 0.1);
    border-color: rgba(248, 113, 113, 0.3);
}

/* Mobile Optimizations for New Features */
@media (max-width: 640px) {
    .bulk-actions,
    .qr-generation-section {
        padding: var(--space-3);
    }

    .action-buttons .grid,
    .generation-controls {
        grid-template-columns: 1fr;
        flex-direction: column;
    }

    .status-indicator {
        font-size: 0.7rem;
        padding: var(--space-1) var(--space-2);
    }

    .qr-stats-grid {
        gap: var(--space-1);
    }

    .qr-stat-card {
        padding: var(--space-2);
    }

    .qr-stat-value {
        font-size: 1rem;
    }

    .generation-info,
    .generation-details {
        flex-direction: column;
        gap: var(--space-1);
        text-align: center;
    }
}

/* Base Styles */
* {
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    margin: 0;
    padding: 0;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    font-size: 16px;
    line-height: 1.6;
    color: var(--text-primary);
    background: var(--background);
    transition: var(--transition);
    overflow-x: hidden;
}

/* Modern Layout Components */
.container {
    width: 100%;
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 var(--space-4);
}

.main-container {
    background: var(--surface);
    border-radius: var(--radius-2xl);
    box-shadow: var(--shadow-lg);
    padding: var(--space-8);
    margin: var(--space-8) auto;
    max-width: 1200px;
    border: 1px solid var(--border-light);
    transition: var(--transition);
}

.header {
    background: linear-gradient(135deg, var(--primary), var(--primary-dark));
    color: var(--white);
    padding: var(--space-8) var(--space-6);
    text-align: center;
    border-radius: var(--radius-xl);
    margin-bottom: var(--space-8);
    position: relative;
    overflow: hidden;
}

.header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.1) 50%, transparent 70%);
    animation: shimmer 3s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.card {
    background: var(--surface);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--border);
    transition: var(--transition);
    overflow: hidden;
}

.card:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-2px);
}

/* Modern Grid System */
.grid {
    display: grid;
    gap: var(--space-6);
}

.grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
.grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
.grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }

/* Responsive Grid */
@media (min-width: 768px) {
    .md\:grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
    .md\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
}

@media (min-width: 1024px) {
    .lg\:grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
    .lg\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
}

/* Section Styles */
.preview-section,
.scanner-section {
    background: var(--surface);
    border-radius: var(--radius-xl);
    padding: var(--space-6);
    box-shadow: var(--shadow);
    border: 1px solid var(--border);
    transition: var(--transition);
    height: fit-content;
}

.preview-section:hover,
.scanner-section:hover {
    box-shadow: var(--shadow-md);
}

.section-title {
    background: linear-gradient(135deg, var(--primary), var(--secondary));
    color: var(--white);
    padding: var(--space-3) var(--space-4);
    font-size: 1.125rem;
    font-weight: 600;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
    margin-bottom: var(--space-4);
    display: inline-flex;
    align-items: center;
    gap: var(--space-2);
    position: relative;
    overflow: hidden;
}

.section-title::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: var(--transition);
}

.section-title:hover::before {
    left: 100%;
}

#current-dataset,
#current-template {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* Modern Modal Styles */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.6);
    backdrop-filter: blur(8px);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    opacity: 0;
    animation: fadeIn 0.3s ease-out forwards;
}

@keyframes fadeIn {
    to { opacity: 1; }
}

.modal-content {
    background: var(--surface);
    border-radius: var(--radius-2xl);
    box-shadow: var(--shadow-xl);
    border: 1px solid var(--border);
    max-width: 90vw;
    max-height: 90vh;
    overflow-y: auto;
    transform: scale(0.9);
    animation: scaleIn 0.3s ease-out forwards;
}

@keyframes scaleIn {
    to { transform: scale(1); }
}

.close-btn {
    position: absolute;
    top: var(--space-4);
    right: var(--space-4);
    background: var(--surface-variant);
    border: 1px solid var(--border);
    border-radius: var(--radius-full);
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    cursor: pointer;
    color: var(--text-secondary);
    transition: var(--transition);
}

.close-btn:hover {
    background: var(--danger);
    color: var(--white);
    transform: scale(1.1);
}

.paper-size-note {
    font-size: 0.85rem;
    color: #6b7280;
    margin-top: 0.5rem;
}

/* Modern Button System */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-2);
    padding: var(--space-3) var(--space-4);
    font-size: 0.875rem;
    font-weight: 500;
    line-height: 1;
    border-radius: var(--radius);
    border: 1px solid transparent;
    cursor: pointer;
    transition: var(--transition);
    text-decoration: none;
    white-space: nowrap;
    user-select: none;
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    pointer-events: none;
}

/* Button Variants */
.btn-primary {
    background: var(--primary);
    color: var(--white);
    border-color: var(--primary);
    box-shadow: var(--shadow-sm);
}

.btn-primary:hover {
    background: var(--primary-dark);
    border-color: var(--primary-dark);
    box-shadow: var(--shadow);
    transform: translateY(-1px);
}

.btn-secondary {
    background: var(--surface-variant);
    color: var(--text-primary);
    border-color: var(--border);
}

.btn-secondary:hover {
    background: var(--gray-200);
    border-color: var(--gray-300);
}

.btn-success {
    background: var(--success);
    color: var(--white);
    border-color: var(--success);
}

.btn-success:hover {
    background: #059669;
    border-color: #059669;
    transform: translateY(-1px);
}

.btn-danger {
    background: var(--danger);
    color: var(--white);
    border-color: var(--danger);
}

.btn-danger:hover {
    background: #dc2626;
    border-color: #dc2626;
    transform: translateY(-1px);
}

/* Button Sizes */
.btn-sm {
    padding: var(--space-2) var(--space-3);
    font-size: 0.75rem;
}

.btn-lg {
    padding: var(--space-4) var(--space-6);
    font-size: 1rem;
}

/* Gradient Button */
.btn-gradient {
    background: linear-gradient(135deg, var(--primary), var(--secondary));
    color: var(--white);
    border: none;
    box-shadow: var(--shadow);
    position: relative;
    overflow: hidden;
}

.btn-gradient::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: var(--transition);
}

.btn-gradient:hover::before {
    left: 100%;
}

.btn-gradient:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

/* Modern Component Styles */

/* Preview Components */
.preview-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--space-4);
}

.preview-frame {
    width: 100%;
    max-width: 500px;
    aspect-ratio: 1;
    background: var(--surface-variant);
    border-radius: var(--radius-xl);
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    box-shadow: var(--shadow-md);
    border: 2px solid var(--border);
    position: relative;
}

.preview-wrapper {
    transform: scale(1);
    transform-origin: center center;
    transition: var(--transition);
}

.id-preview-container {
    position: relative;
    background: var(--white);
    border-radius: var(--radius);
    overflow: hidden;
    box-shadow: var(--shadow);
}

.template-image {
    width: 100%;
    height: 100%;
    object-fit: contain;
    display: block;
}

.id-overlay {
    position: absolute;
    inset: 0;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    pointer-events: none;
    z-index: 2;
    color: var(--text-primary);
    text-align: center;
    padding: var(--space-4);
}

.preview-id {
    font-size: 2rem;
    font-weight: 700;
    color: var(--primary);
    text-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.preview-name {
    font-size: 1.25rem;
    font-weight: 600;
    margin: var(--space-2) 0;
}

.preview-position {
    font-size: 1rem;
    color: var(--text-secondary);
    margin-bottom: var(--space-1);
}

.preview-company {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--primary);
}

.preview-spacer {
    height: var(--space-4);
}

.preview-controls {
    width: 100%;
    max-width: 500px;
}

/* Scanner Components */
.scanner-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--space-3);
}

.qr-reader {
    width: 100%;
    max-width: 400px;
    aspect-ratio: 4/3;
    background: var(--surface-variant);
    border-radius: var(--radius-lg);
    border: 2px solid var(--border);
    overflow: hidden;
    box-shadow: var(--shadow);
}

.scanner-status {
    text-align: center;
}

/* ID Info Card */
.id-info-card {
    background: var(--surface);
    border-radius: var(--radius-xl);
    border: 1px solid var(--border);
    overflow: hidden;
}

.id-info-content {
    padding: var(--space-6);
}

.info-grid {
    display: grid;
    gap: var(--space-4);
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--space-3);
    background: var(--surface-variant);
    border-radius: var(--radius);
    border: 1px solid var(--border-light);
    transition: var(--transition);
}

.info-item:hover {
    background: var(--gray-100);
    transform: translateY(-1px);
}

.info-label {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    font-weight: 500;
    color: var(--text-secondary);
    font-size: 0.875rem;
}

.info-value {
    font-weight: 600;
    color: var(--text-primary);
    text-align: right;
    font-size: 0.875rem;
}

/* Loading Spinner */
.loading-spinner {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--space-2);
}

.spinner {
    display: flex;
    gap: var(--space-1);
}

.spinner span {
    width: 4px;
    height: 20px;
    background: linear-gradient(to top, var(--primary), var(--primary-light));
    border-radius: var(--radius-sm);
    animation: bounce 1.4s ease-in-out infinite both;
}

.spinner span:nth-child(1) { animation-delay: -0.32s; }
.spinner span:nth-child(2) { animation-delay: -0.16s; }
.spinner span:nth-child(3) { animation-delay: 0s; }
.spinner span:nth-child(4) { animation-delay: 0.16s; }

@keyframes bounce {
    0%, 80%, 100% {
        transform: scaleY(0.6);
    }
    40% {
        transform: scaleY(1);
    }
}

/* Alert Components */
.alert-container {
    padding: var(--space-4);
    border-radius: var(--radius-lg);
    border: 1px solid;
    display: flex;
    align-items: center;
    gap: var(--space-3);
    font-weight: 500;
}

.alert-success {
    background: #f0fdf4;
    border-color: #bbf7d0;
    color: #166534;
}

.alert-warning {
    background: #fffbeb;
    border-color: #fed7aa;
    color: #92400e;
}

.alert-danger {
    background: #fef2f2;
    border-color: #fecaca;
    color: #991b1b;
}

.alert-info {
    background: #eff6ff;
    border-color: #bfdbfe;
    color: #1e40af;
}

/* Utility Classes */
.hidden { display: none !important; }
.flex { display: flex; }
.items-center { align-items: center; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.space-x-2 > * + * { margin-left: var(--space-2); }
.space-x-3 > * + * { margin-left: var(--space-3); }
.space-x-4 > * + * { margin-left: var(--space-4); }
.w-full { width: 100%; }
.w-2 { width: 0.5rem; }
.w-10 { width: 2.5rem; }
.h-2 { height: 0.5rem; }
.h-10 { height: 2.5rem; }
.text-sm { font-size: 0.875rem; }
.text-xl { font-size: 1.25rem; }
.font-bold { font-weight: 700; }
.rounded-lg { border-radius: var(--radius-lg); }
.rounded-full { border-radius: var(--radius-full); }
.mt-2 { margin-top: var(--space-2); }
.mt-3 { margin-top: var(--space-3); }
.mt-4 { margin-top: var(--space-4); }
.mb-4 { margin-bottom: var(--space-4); }
.mb-6 { margin-bottom: var(--space-6); }
.px-4 { padding-left: var(--space-4); padding-right: var(--space-4); }
.py-3 { padding-top: var(--space-3); padding-bottom: var(--space-3); }
.py-4 { padding-top: var(--space-4); padding-bottom: var(--space-4); }
.py-8 { padding-top: var(--space-8); padding-bottom: var(--space-8); }
.pt-4 { padding-top: var(--space-4); }

/* Animation Classes */
.animate-pulse {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* Responsive Design & Mobile Optimization */

/* Mobile First Approach */
@media (max-width: 640px) {
    .container {
        padding: 0 var(--space-3);
    }

    .main-container {
        margin: var(--space-4) auto;
        padding: var(--space-4);
        border-radius: var(--radius-lg);
    }

    .header {
        padding: var(--space-4);
        border-radius: var(--radius-lg);
        margin-bottom: var(--space-4);
    }

    .header h1 {
        font-size: 1.125rem;
    }

    .header p {
        font-size: 0.75rem;
    }

    /* Mobile Grid */
    .grid {
        grid-template-columns: 1fr;
        gap: var(--space-4);
    }

    /* Mobile Sections */
    .preview-section,
    .scanner-section {
        padding: var(--space-4);
    }

    .section-title {
        font-size: 1rem;
        padding: var(--space-2) var(--space-3);
    }

    /* Mobile Preview */
    .preview-frame {
        max-width: 100%;
        aspect-ratio: 1.2;
    }

    .preview-id {
        font-size: 1.5rem;
    }

    .preview-name {
        font-size: 1rem;
    }

    .preview-position,
    .preview-company {
        font-size: 0.875rem;
    }

    /* Mobile Scanner */
    .qr-reader {
        max-width: 100%;
        aspect-ratio: 1;
    }

    /* Mobile Info Grid */
    .info-grid {
        gap: var(--space-3);
    }

    .info-item {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--space-2);
        padding: var(--space-3);
    }

    .info-value {
        text-align: left;
        font-size: 1rem;
        font-weight: 700;
    }

    /* Mobile Buttons */
    .btn {
        padding: var(--space-3) var(--space-4);
        font-size: 1rem;
        min-height: 48px; /* Touch-friendly */
    }

    .btn-sm {
        padding: var(--space-2) var(--space-3);
        min-height: 40px;
    }

    .btn-lg {
        padding: var(--space-4) var(--space-6);
        font-size: 1.125rem;
        min-height: 56px;
    }

    /* Mobile Header Actions */
    .header .flex {
        flex-wrap: wrap;
        gap: var(--space-2);
    }

    /* Mobile Modal */
    .modal-content {
        max-width: 95vw;
        margin: var(--space-4);
    }
}

/* Tablet Optimization */
@media (min-width: 641px) and (max-width: 1024px) {
    .container {
        padding: 0 var(--space-6);
    }

    .grid {
        gap: var(--space-6);
    }

    .preview-frame {
        max-width: 400px;
    }

    .qr-reader {
        max-width: 350px;
    }

    .info-item {
        padding: var(--space-4);
    }
}

/* Desktop Optimization */
@media (min-width: 1025px) {
    .container {
        padding: 0 var(--space-8);
    }

    .main-container {
        padding: var(--space-10);
    }

    .grid {
        gap: var(--space-8);
    }

    /* Desktop Hover Effects */
    .preview-section:hover {
        transform: translateY(-4px);
        box-shadow: var(--shadow-xl);
    }

    .scanner-section:hover {
        transform: translateY(-4px);
        box-shadow: var(--shadow-xl);
    }

    .info-item:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-md);
    }
}

/* Touch Device Optimizations */
@media (hover: none) and (pointer: coarse) {
    /* Remove hover effects on touch devices */
    .btn:hover,
    .card:hover,
    .info-item:hover,
    .preview-section:hover,
    .scanner-section:hover {
        transform: none;
        box-shadow: inherit;
    }

    /* Larger touch targets */
    .btn {
        min-height: 48px;
        padding: var(--space-3) var(--space-4);
    }

    .close-btn {
        width: 48px;
        height: 48px;
    }

    /* Better spacing for touch */
    .info-item {
        padding: var(--space-4);
        margin-bottom: var(--space-2);
    }
}

/* High DPI Display Support */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .template-image {
        image-rendering: -webkit-optimize-contrast;
        image-rendering: crisp-edges;
    }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }

    .animate-pulse {
        animation: none;
    }

    .spinner span {
        animation: none;
        transform: scaleY(1);
    }
}

/* Print Styles */
@media print {
    body {
        background: white !important;
        color: black !important;
    }

    .header,
    .scanner-section,
    .btn,
    .modal-overlay {
        display: none !important;
    }

    .preview-section {
        width: 100% !important;
        max-width: none !important;
        box-shadow: none !important;
        border: none !important;
        padding: 0 !important;
        margin: 0 !important;
    }

    .preview-frame {
        width: 100% !important;
        max-width: none !important;
        box-shadow: none !important;
        border: none !important;
    }

    .id-preview-container {
        width: 100% !important;
        height: auto !important;
    }
}

#activatePanel {
    background: white;
    padding: 2rem;
    border-radius: 16px;
    border: 1px solid var(--border-color);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.05);
    margin-bottom: 2rem;
    position: relative;
    overflow: hidden;
}

#activatePanel strong {
    font-size: 1rem;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    background: linear-gradient(90deg, #782fff, #c084fc);
    color: white;
    padding: 0.4rem 0.8rem;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(120, 47, 255, 0.2);
    margin-bottom: 0.5rem;
}

#activatePanel span {
    font-family: monospace;
    font-size: 0.9rem;
    color: #1f2937;
    word-break: break-all;
}

#activatePanel .btn-outline-primary {
    padding: 0.35rem 0.9rem;
    font-size: 0.85rem;
    font-weight: 500;
    border-radius: 8px;
    border: 2px solid #7c3aed;
    background: white;
    color: #7c3aed;
    transition: all 0.2s ease;
}

#activatePanel .btn-outline-primary:hover {
    background: #7c3aed;
    color: white;
    box-shadow: 0 4px 12px rgba(124, 58, 237, 0.2);
}

#activatePanel .btn-success {
    background: linear-gradient(90deg, #782fff, #c084fc);
    border: none;
    color: white;
    font-weight: 600;
    padding: 0.6rem 1.5rem;
    font-size: 0.95rem;
    border-radius: 100px;
    box-shadow: 0 0.7em 1.5em -0.5em rgba(120, 47, 255, 0.5);
    transition: all 0.3s ease;
}

#activatePanel .btn-success:hover {
    box-shadow: 0 0.5em 1.5em -0.5em rgba(124, 58, 237, 0.6);
    transform: translateY(-2px);
}

#activatePanel .paper-size-control {
    margin-top: 2rem;
}

#activatePanel label {
    font-weight: 600;
    font-size: 0.95rem;
    color: #374151;
    margin-bottom: 0.25rem;
}

#activatePanel select {
    padding: 0.5rem 0.75rem;
    border-radius: 8px;
    border: 1px solid var(--border-color);
    background: #f9fafb;
    font-size: 0.9rem;
    width: 100%;
    max-width: 300px;
}

#customSizeFields {
    margin-top: 1rem;
    padding: 1rem;
    border-radius: 8px;
    background: #f9f9fb;
    border: 1px solid #e5e7eb;
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

#customSizeFields input {
    width: 140px;
    padding: 0.5rem 0.75rem;
    border-radius: 6px;
    border: 1px solid #d1d5db;
    background: white;
    font-size: 0.9rem;
}

@media (max-width: 576px) {
    #customSizeFields {
        flex-direction: column;
    }

    #customSizeFields input {
        width: 100%;
    }
}

#scaledPreviewWrapper {
    transform: scale(1);
    transform-origin: center center;
}

.content-area,
.row.g-4.align-items-stretch {
    display: flex;
    flex-wrap: nowrap;
    align-items: flex-start;
}

.col-lg-7,
.col-lg-5 {
    flex: 1;
    max-width: 50%;
}

.id-card {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    border: 2px solid var(--border-color);
    transition: all 0.3s ease;
    min-height: 300px;
    box-sizing: border-box;
    margin: 0 auto;
    overflow: hidden;
}

.id-card.loaded {
    border-color: var(--success-color);
    box-shadow: 0 10px 30px rgba(5, 150, 105, 0.2);
}

.id-card-header {
    text-align: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid var(--border-color);
}

.id-number {
    font-size: 2rem;
    font-weight: bold;
    color: var(--primary-color);
    margin: 0;
}

.employee-info {
    margin-top: 1rem;
}

.info-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid #f1f5f9;
}

.info-label {
    font-weight: 600;
    color: #64748b;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.info-value {
    font-weight: 500;
    color: #1e293b;
    text-align: right;
}

.scanner-controls {
    text-align: center;
    margin-bottom: 2rem;
}

.scan-mode-toggle {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-bottom: 2rem;
}

.mode-btn {
    padding: 0.75rem 1.5rem;
    border: 2px solid var(--border-color);
    background: white;
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.mode-btn.active {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.mode-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

#reader,
.scanner-section .border.bg-white.shadow-sm,
.border.rounded.p-3.bg-white.shadow-sm {
    background-color: #ffffff !important;
    border: none;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.05);
}

#usb-input {
    width: 100%;
    padding: 1rem;
    font-size: 1.2rem;
    border: 2px solid var(--border-color);
    border-radius: 10px;
    text-align: center;
    transition: all 0.3s ease;
    display: none;
}

#usb-input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
    outline: none;
}

.action-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-top: 2rem;
}

.btn-custom {
    padding: 0.75rem 2rem;
    border-radius: 10px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
    border: none;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

@media print {
    #paperBoundary {
        background: none !important;
        padding: 0 !important;
    }
    body {
        background: white !important;
    }
    .main-container,
    .preview-section,
    .id-card {
        box-shadow: none !important;
        border: none !important;
        margin: 0 !important;
        padding: 0 !important;
        width: 100%;
        height: 100%;
    }
    .id-card {
        page-break-before: always;
    }
    .scanner-section,
    .header,
    .action-buttons,
    .setting-btn,
    .status-indicator,
    .scanner-controls {
        display: none !important;
    }
    .preview-section {
        width: 100% !important;
        border: none !important;
        padding: 0 !important;
        margin: 0 auto !important;
    }
    #id-preview {
        width: 100%;
        height: 100%;
    }
}

.btn-print {
    background: var(--success-color);
    color: white;
}

.btn-print:hover {
    background: #047857;
    transform: translateY(-2px);
}

.btn-reset {
    background: var(--warning-color);
    color: white;
}

.btn-reset:hover {
    background: #b45309;
    transform: translateY(-2px);
}

.status-indicator {
    position: absolute;
    top: 1rem;
    right: 1rem;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: var(--danger-color);
    animation: pulse 2s infinite;
}

.status-indicator.connected {
    background: var(--success-color);
}

@keyframes pulse {
    0% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
    100% {
        opacity: 1;
    }
}

.loading-spinner {
    display: none;
    margin: 1rem auto;
    width: 40px;
    height: 40px;
    border: 4px solid #f3f4f6;
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

.alert-custom {
    border-radius: 10px;
    border: none;
    padding: 1rem;
    margin: 1rem 0;
    display: none;
}

@media (max-width: 991px) {
    .content-area,
    .row.g-4.align-items-stretch {
        flex-direction: column;
        flex-wrap: wrap;
    }
    .col-lg-7,
    .col-lg-5 {
        max-width: 100%;
    }
    #reader {
        max-width: 100% !important;
    }
}

#scaledPreviewWrapper {
    transform: scale(1);
    transform-origin: center center;
}

#paperBoundary {
    width: 500px;
    height: 500px;
    overflow: hidden;
    border-radius: 10px;
    margin: auto;
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-shrink: 0;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    /* Light border to contain the blur */
    border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Blurred background effect */
#paperBoundary::before {
    content: "";
    position: absolute;
    inset: 0;
    background-image: var(--template-bg-image);
    background-size: cover;
    background-position: center;
    /* Blur effect */
    filter: blur(12px) brightness(1.05);
    z-index: -1;
    /* Slight overlay for better contrast */
    background-color: rgba(255, 255, 255, 0.15);
    background-blend-mode: overlay;
}

/* Preview container */
#idPreviewContainer {
    background-color: white;
    position: relative;
    z-index: 1;
    box-sizing: border-box;
    overflow: hidden;
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 5px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.id-overlay div {
    font-weight: 600;
    font-size: 1rem;
    color: #111827;
    /* Subtle white shadow for better contrast */
    text-shadow: 
        0 1px 1px rgba(255, 255, 255, 0.7),
        0 0 8px rgba(255, 255, 255, 0.4);
}

/* Specific styling for the header text */
.id-overlay div:first-child {
    font-size: 1.8rem;
    font-weight: 700;
    letter-spacing: 0.05em;
}

/* Styling for the ID number */
.id-overlay div:nth-child(2) {
    font-size: 1.5rem;
    margin: 0.5rem 0;
}

h5.mb-3 {
    background: #dfd4bd;
    color: #333;
    padding: 0.4rem 0.8rem;
    border-radius: 6px;
    display: inline-block;
    font-weight: 600;
}
/* Trademark watermark - non-interactive */
.trademark-mark {
  pointer-events: none; 
  user-select: none;
  -webkit-user-drag: none;
}

/* Footer styling */
footer {
  background: rgba(255,255,255,0.9);
  position: relative;
  z-index: 1;
}

/* loading */
/* From Uiverse.io by satyamchaudharydev */ 
.spinner {
    --gap: 5px;
    --height: 23px;
    width: 100px;
    height: 100px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--gap);
}

.spinner span {
    background: linear-gradient(to top, #9b23ea, #a64bf4);
    width: 6px;
    height: var(--height);
    animation: grow 1s ease-in-out infinite;
    border-radius: 3px;
}

.spinner span:nth-child(2) {
    animation-delay: 0.15s;
}

.spinner span:nth-child(3) {
    animation-delay: 0.3s;
}

.spinner span:nth-child(4) {
    animation-delay: 0.475s;
}

@keyframes grow {
    0%, 100% {
        transform: scaleY(1);
    }
    50% {
        transform: scaleY(1.8);
    }
}


/* first strat */
/* Purple Theme */
.setup-container {
    max-width: 800px;
    margin: 2rem auto;
    padding: 2rem;
    background: white;
    border-radius: 10px;
    box-shadow: 0 4px 20px rgba(128, 0, 128, 0.15);
    position: relative;
    border-top: 4px solid #800080;
}

.step-number {
    display: inline-block;
    width: 30px;
    height: 30px;
    background: linear-gradient(135deg, #8a2be2, #9400d3);
    color: white;
    text-align: center;
    border-radius: 50%;
    margin-right: 10px;
    line-height: 30px;
    font-weight: bold;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.upload-area {
    border: 2px dashed #d8bfd8;
    padding: 2rem;
    text-align: center;
    border-radius: 8px;
    margin-bottom: 1rem;
    cursor: pointer;
    transition: all 0.3s;
    background: #faf5ff;
}
.upload-area:hover {
    border-color: #9370db;
    background: #f3e9ff;
    transform: translateY(-2px);
}

.btn-primary {
    background: linear-gradient(135deg, #8a2be2, #9400d3);
    border: none;
    padding: 10px 25px;
    font-weight: 500;
}
.btn-primary:hover {
    background: linear-gradient(135deg, #9400d3, #8a2be2);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(138, 43, 226, 0.3);
}

.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.9);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    display: none;
}
.loading-text {
    margin-top: 15px;
    color: #800080;
    font-weight: 500;
}

/* Other existing styles */
.step {
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #eee;
}
.file-list {
    max-height: 200px;
    overflow-y: auto;
}
.header-logo {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    margin-bottom: 1rem;
}
    .header-logo img {
    width: 40px;
    height: 40px;
}
.footer {
    text-align: center;
    margin-top: 2rem;
    padding-top: 1rem;
    border-top: 1px solid #eee;
    font-size: 0.9rem;
    color: #666;
}
.footer img {
    width: 20px;
    height: 20px;
    vertical-align: middle;
    margin-right: 5px;
}
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    display: none;
}
